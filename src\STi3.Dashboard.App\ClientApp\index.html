<!doctype html><html lang="pt-br"><head><meta charset="utf-8"><meta http-equiv="X-UA-Compatible" content="IE=edge"><meta name="viewport" content="width=device-width,initial-scale=1"><link href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i&display=swap" rel="stylesheet"><link rel="icon" href="/favicon.ico"/><title>Power Stock</title><link href="/static/css/2.d34346ea.chunk.css" rel="stylesheet"><link href="/static/css/main.ce2dc29c.chunk.css" rel="stylesheet"></head><body><noscript><strong>Javascript desabilitado.</strong></noscript><div id="root"></div><script>!function(e){function t(t){for(var n,p,i=t[0],l=t[1],a=t[2],c=0,s=[];c<i.length;c++)p=i[c],Object.prototype.hasOwnProperty.call(o,p)&&o[p]&&s.push(o[p][0]),o[p]=0;for(n in l)Object.prototype.hasOwnProperty.call(l,n)&&(e[n]=l[n]);for(f&&f(t);s.length;)s.shift()();return u.push.apply(u,a||[]),r()}function r(){for(var e,t=0;t<u.length;t++){for(var r=u[t],n=!0,i=1;i<r.length;i++){var l=r[i];0!==o[l]&&(n=!1)}n&&(u.splice(t--,1),e=p(p.s=r[0]))}return e}var n={},o={1:0},u=[];function p(t){if(n[t])return n[t].exports;var r=n[t]={i:t,l:!1,exports:{}};return e[t].call(r.exports,r,r.exports,p),r.l=!0,r.exports}p.m=e,p.c=n,p.d=function(e,t,r){p.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},p.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},p.t=function(e,t){if(1&t&&(e=p(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(p.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)p.d(r,n,function(t){return e[t]}.bind(null,n));return r},p.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return p.d(t,"a",t),t},p.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},p.p="/";var i=this["webpackJsonpsti3-app"]=this["webpackJsonpsti3-app"]||[],l=i.push.bind(i);i.push=t,i=i.slice();for(var a=0;a<i.length;a++)t(i[a]);var f=l;r()}([])</script><script src="/static/js/2.cb559268.chunk.js"></script><script src="/static/js/main.89d16d01.chunk.js"></script></body></html>